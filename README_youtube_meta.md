# YouTube视频元信息获取器

这个脚本可以根据给定的YouTube视频ID或URL获取视频的详细元信息，并将结果保存为JSON格式。

## 功能特性

- 支持多种输入格式：
  - 完整YouTube URL: `https://www.youtube.com/watch?v=VIDEO_ID`
  - 短链接: `https://youtu.be/VIDEO_ID`
  - Shorts URL: `https://youtube.com/shorts/VIDEO_ID`
  - 直接视频ID: `VIDEO_ID`

- 获取的元信息包括：
  - 视频基本信息（标题、时长、作者）
  - 互动数据（观看次数、点赞数、评论数）
  - 发布信息（发布时间、频道订阅数）
  - 内容信息（描述、标签列表）
  - 视频质量信息

## 安装依赖

```bash
pip install requests loguru
```

## 使用方法

### 1. 命令行使用

```bash
# 基本使用
python youtube_video_meta.py "dQw4w9WgXcQ"

# 使用完整URL
python youtube_video_meta.py "https://www.youtube.com/watch?v=dQw4w9WgXcQ"

# 指定输出文件
python youtube_video_meta.py "dQw4w9WgXcQ" -o "my_video_meta.json"

# 只打印到控制台，不保存文件
python youtube_video_meta.py "dQw4w9WgXcQ" --print-only

# 使用代理
python youtube_video_meta.py "dQw4w9WgXcQ" --proxy
```

### 2. 作为模块使用

```python
from youtube_video_meta import YouTubeVideoMeta

# 创建实例
youtube_meta = YouTubeVideoMeta(use_proxy=False)

# 获取视频元信息
metadata = youtube_meta.get_video_metadata("dQw4w9WgXcQ")

if metadata:
    print(f"标题: {metadata['title']}")
    print(f"作者: {metadata['author']}")
    print(f"观看次数: {metadata['view_count']}")
    
    # 保存为JSON文件
    youtube_meta.save_to_json(metadata, "video_metadata.json")
```

### 3. 批量处理

```python
from youtube_video_meta import YouTubeVideoMeta
import json

youtube_meta = YouTubeVideoMeta()
video_ids = ["dQw4w9WgXcQ", "9bZkp7q19f0"]

all_metadata = []
for video_id in video_ids:
    metadata = youtube_meta.get_video_metadata(video_id)
    if metadata:
        all_metadata.append(metadata)

# 保存所有元信息
with open("batch_metadata.json", "w", encoding="utf-8") as f:
    json.dump(all_metadata, f, ensure_ascii=False, indent=2)
```

## 输出格式

脚本输出的JSON包含以下字段：

```json
{
  "video_id": "视频ID",
  "title": "视频标题",
  "length_time": "视频时长 (MM:SS格式)",
  "view_count": "观看次数",
  "like_count": "点赞数",
  "dislike_count": "不喜欢数",
  "share_count": "分享数",
  "comment_count": "评论数",
  "publish_time": "发布时间",
  "author": "作者/频道名",
  "subscriber_count": "订阅者数量",
  "description": "视频描述",
  "tag_list": ["标签1", "标签2"],
  "video_quality_info": {
    "额外的视频质量信息"
  }
}
```

## 命令行参数

- `video_input`: 必需参数，YouTube视频ID、短链接或完整URL
- `-o, --output`: 可选，指定输出JSON文件路径
- `--proxy`: 可选，使用代理服务器
- `--print-only`: 可选，只打印到控制台，不保存文件

## 示例

运行示例脚本：

```bash
python example_usage.py
```

这将演示如何使用不同格式的输入获取视频元信息。

## 注意事项

1. 某些视频可能因为地区限制或隐私设置无法获取完整信息
2. 频繁请求可能触发YouTube的反爬虫机制，建议适当添加延时
3. 如果遇到网络问题，脚本会自动重试最多3次
4. 代理设置是可选的，根据网络环境决定是否使用

## 错误处理

脚本包含完善的错误处理机制：
- 自动重试机制（最多3次）
- 详细的日志输出
- 输入格式验证
- 网络异常处理

## 依赖说明

- `requests`: HTTP请求库
- `loguru`: 日志记录库
- `argparse`: 命令行参数解析（Python标准库）
- `json`: JSON处理（Python标准库）
- `re`: 正则表达式（Python标准库）
- `urllib.parse`: URL解析（Python标准库）
