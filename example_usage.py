#!/usr/bin/env python3
"""
YouTube视频元信息获取器使用示例
"""

from youtube_video_meta import YouTubeVideoMeta
import json

def example_usage():
    """
    演示如何使用YouTubeVideoMeta类
    """
    # 创建实例（可选择是否使用代理）
    youtube_meta = YouTubeVideoMeta(use_proxy=False)
    
    # 测试不同格式的输入
    test_inputs = [
        # 完整的YouTube URL
        "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
        
        # 短链接
        "https://youtu.be/dQw4w9WgXcQ",
        
        # Shorts URL
        "https://youtube.com/shorts/dQw4w9WgXcQ",
        
        # 直接的video_id
        "dQw4w9WgXcQ"
    ]
    
    print("=== YouTube视频元信息获取器示例 ===\n")
    
    for i, video_input in enumerate(test_inputs, 1):
        print(f"示例 {i}: {video_input}")
        print("-" * 50)
        
        # 获取视频元信息
        metadata = youtube_meta.get_video_metadata(video_input)
        
        if metadata:
            # 打印关键信息
            print(f"视频ID: {metadata['video_id']}")
            print(f"标题: {metadata['title']}")
            print(f"时长: {metadata['length_time']}")
            print(f"作者: {metadata['author']}")
            print(f"观看次数: {metadata['view_count']}")
            print(f"点赞数: {metadata['like_count']}")
            print(f"发布时间: {metadata['publish_time']}")
            
            # 保存为JSON文件
            output_file = f"example_{metadata['video_id']}_metadata.json"
            youtube_meta.save_to_json(metadata, output_file)
            print(f"详细信息已保存到: {output_file}")
        else:
            print("❌ 获取元信息失败")
        
        print("\n" + "="*60 + "\n")


def batch_process_example():
    """
    批量处理多个视频的示例
    """
    youtube_meta = YouTubeVideoMeta(use_proxy=False)
    
    # 视频ID列表
    video_ids = [
        "dQw4w9WgXcQ",  # Rick Roll
        "9bZkp7q19f0",  # PSY - GANGNAM STYLE
        # 添加更多视频ID...
    ]
    
    print("=== 批量处理示例 ===\n")
    
    all_metadata = []
    
    for video_id in video_ids:
        print(f"正在处理: {video_id}")
        metadata = youtube_meta.get_video_metadata(video_id)
        
        if metadata:
            all_metadata.append(metadata)
            print(f"✅ 成功获取: {metadata['title']}")
        else:
            print(f"❌ 失败: {video_id}")
        print()
    
    # 保存所有元信息到一个文件
    if all_metadata:
        with open("batch_metadata.json", "w", encoding="utf-8") as f:
            json.dump(all_metadata, f, ensure_ascii=False, indent=2)
        print(f"批量处理完成，共处理 {len(all_metadata)} 个视频")
        print("所有元信息已保存到: batch_metadata.json")


if __name__ == "__main__":
    # 运行基本示例
    example_usage()
    
    # 运行批量处理示例
    # batch_process_example()
