import json
import re
import requests
from loguru import logger
import argparse
import sys
from urllib.parse import urlparse, parse_qs


class YouTubeVideoMeta:
    def __init__(self, use_proxy=False):
        self.proxies = {
            'http': 'http://5851b070f69-zone-adam:<EMAIL>:7788',
        } if use_proxy else None
        
        self.headers = {
            'accept': '*/*',
            'accept-language': 'zh-CN,zh;q=0.9',
            'cache-control': 'no-cache',
            'content-type': 'application/json',
            'origin': 'https://www.youtube.com',
            'pragma': 'no-cache',
            'priority': 'u=1, i',
            'referer': 'https://www.youtube.com/',
            'sec-ch-ua': '"Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
            'sec-ch-ua-arch': '"x86"',
            'sec-ch-ua-bitness': '"64"',
            'sec-ch-ua-form-factors': '"Desktop"',
            'sec-ch-ua-full-version': '"135.0.7049.96"',
            'sec-ch-ua-full-version-list': '"Google Chrome";v="135.0.7049.96", "Not-A.Brand";v="8.0.0.0", "Chromium";v="135.0.7049.96"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-model': '""',
            'sec-ch-ua-platform': '"Windows"',
            'sec-ch-ua-platform-version': '"19.0.0"',
            'sec-ch-ua-wow64': '?0',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'same-origin',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36',
            'x-goog-authuser': '0',
            'x-origin': 'https://www.youtube.com',
            'x-youtube-bootstrap-logged-in': 'true',
            'x-youtube-client-name': '1',
            'x-youtube-client-version': '2.20250710.09.00',
        }

    def extract_video_id(self, input_str):
        """
        从各种YouTube URL格式或直接的video_id中提取video_id
        支持格式:
        - https://www.youtube.com/watch?v=VIDEO_ID
        - https://youtu.be/VIDEO_ID
        - https://youtube.com/shorts/VIDEO_ID
        - VIDEO_ID (直接的video_id)
        """
        # 如果输入看起来像是一个URL
        if 'youtube.com' in input_str or 'youtu.be' in input_str:
            # 处理标准watch URL
            if 'watch?v=' in input_str:
                parsed_url = urlparse(input_str)
                query_params = parse_qs(parsed_url.query)
                if 'v' in query_params:
                    return query_params['v'][0]
            
            # 处理短链接 youtu.be
            elif 'youtu.be/' in input_str:
                return input_str.split('youtu.be/')[-1].split('?')[0]
            
            # 处理shorts URL
            elif '/shorts/' in input_str:
                return input_str.split('/shorts/')[-1].split('?')[0]
        
        # 如果不是URL，假设是直接的video_id
        # YouTube video_id通常是11个字符的字母数字组合
        video_id = input_str.strip()
        if len(video_id) == 11 and video_id.replace('-', '').replace('_', '').isalnum():
            return video_id
        
        raise ValueError(f"无法从输入中提取有效的video_id: {input_str}")

    def get_video_metadata(self, video_input, max_retries=3):
        """
        获取YouTube视频的详细元信息
        """
        try:
            video_id = self.extract_video_id(video_input)
            logger.info(f"提取到video_id: {video_id}")
        except ValueError as e:
            logger.error(str(e))
            return None

        for attempt in range(max_retries):
            try:
                params = {'v': video_id}
                
                response = requests.get(
                    'https://www.youtube.com/watch', 
                    params=params, 
                    headers=self.headers,
                    proxies=self.proxies
                )
                
                if response.status_code != 200:
                    logger.error(f"HTTP请求失败，状态码: {response.status_code}")
                    continue
                
                if 'twoColumnWatchNextResults' not in response.text:
                    logger.error(f"页面结构不符合预期，可能是视频不存在或被限制访问")
                    continue
                
                # 提取ytInitialData
                content_match = re.search(r'var ytInitialData = (.*?)</script>', response.text, re.S)
                if not content_match:
                    logger.error("无法找到ytInitialData")
                    continue
                
                content = content_match.group(1).strip(';')
                result = json.loads(content)
                
                # 初始化元信息字典
                metadata = {
                    'video_id': video_id,
                    'title': '',
                    'length_time': '',
                    'view_count': '',
                    'like_count': '',
                    'dislike_count': '',
                    'share_count': '',
                    'comment_count': '',
                    'publish_time': '',
                    'author': '',
                    'subscriber_count': '',
                    'description': '',
                    'tag_list': [],
                    'video_quality_info': {}
                }
                
                # 解析视频基本信息
                contents = result.get('contents', {}).get('twoColumnWatchNextResults', {}).get('results', {}).get('results', {}).get('contents', [])
                
                for item in contents:
                    # 主要视频信息
                    if 'videoPrimaryInfoRenderer' in item:
                        primary_info = item['videoPrimaryInfoRenderer']
                        
                        # 标题
                        if 'title' in primary_info:
                            metadata['title'] = primary_info['title']['runs'][0]['text']
                        
                        # 观看次数
                        if 'viewCount' in primary_info:
                            metadata['view_count'] = primary_info['viewCount']['videoViewCountRenderer']['originalViewCount']
                        
                        # 发布时间
                        if 'dateText' in primary_info:
                            metadata['publish_time'] = primary_info['dateText']['simpleText']
                        
                        # 点赞、分享等信息
                        if 'videoActions' in primary_info:
                            buttons = primary_info['videoActions']['menuRenderer']['topLevelButtons']
                            for button in buttons:
                                if 'segmentedLikeDislikeButtonViewModel' in button:
                                    like_info = button['segmentedLikeDislikeButtonViewModel']
                                    # 点赞数
                                    like_count = like_info['likeButtonViewModel']['likeButtonViewModel']['toggleButtonViewModel']['toggleButtonViewModel']['toggledButtonViewModel']['buttonViewModel']['title']
                                    metadata['like_count'] = like_count
                                    
                                    # 不喜欢数
                                    dislike_info = like_info['dislikeButtonViewModel']['dislikeButtonViewModel']['toggleButtonViewModel']['toggleButtonViewModel']['toggledButtonViewModel']['buttonViewModel']['title']
                                    metadata['dislike_count'] = '0' if dislike_info == '不喜欢' else dislike_info
                                
                                elif 'buttonViewModel' in button:
                                    share_title = button['buttonViewModel']['title']
                                    metadata['share_count'] = "0" if share_title == '分享' else share_title
                    
                    # 次要视频信息（作者、描述等）
                    elif 'videoSecondaryInfoRenderer' in item:
                        secondary_info = item['videoSecondaryInfoRenderer']
                        
                        # 作者信息
                        if 'owner' in secondary_info:
                            owner_info = secondary_info['owner']['videoOwnerRenderer']
                            metadata['author'] = owner_info['title']['runs'][0]['text']
                            if 'subscriberCountText' in owner_info:
                                metadata['subscriber_count'] = owner_info['subscriberCountText']['simpleText']
                        
                        # 描述和标签
                        if 'attributedDescription' in secondary_info:
                            description = secondary_info['attributedDescription']['content']
                            metadata['description'] = description
                            
                            # 提取标签
                            first_line = description.split('\n')[0]
                            tags = []
                            for tag in first_line.split(' #'):
                                if '#' in tag:
                                    tags.append(tag)
                                elif tag.strip():
                                    tags.append("#" + tag)
                            metadata['tag_list'] = tags
                
                # 解析评论数
                engagement_panels = result.get('engagementPanels', [])
                for panel in engagement_panels:
                    if 'engagementPanelSectionListRenderer' in panel:
                        panel_data = panel['engagementPanelSectionListRenderer']
                        if 'header' in panel_data and 'engagementPanelTitleHeaderRenderer' in panel_data['header']:
                            header = panel_data['header']['engagementPanelTitleHeaderRenderer']
                            if 'contextualInfo' in header:
                                metadata['comment_count'] = header['contextualInfo']['runs'][0]['text']
                                break
                
                # 获取视频时长和质量信息
                self._extract_video_quality_info(result, metadata)
                
                logger.info(f"成功获取视频 {video_id} 的元信息")
                return metadata
                
            except json.JSONDecodeError as e:
                logger.error(f"JSON解析失败 (尝试 {attempt + 1}/{max_retries}): {e}")
            except Exception as e:
                logger.error(f"获取视频元信息失败 (尝试 {attempt + 1}/{max_retries}): {e}")
            
            if attempt < max_retries - 1:
                logger.info("等待重试...")
                import time
                time.sleep(2)
        
        logger.error(f"在 {max_retries} 次尝试后仍无法获取视频元信息")
        return None

    def _extract_video_quality_info(self, result, metadata):
        """
        提取视频质量和时长信息
        """
        try:
            # 尝试从playerMicroformatRenderer获取时长
            microformat = result.get('microformat', {}).get('playerMicroformatRenderer', {})
            if 'lengthSeconds' in microformat:
                seconds = int(microformat['lengthSeconds'])
                minutes = seconds // 60
                remaining_seconds = seconds % 60
                metadata['length_time'] = f"{minutes}:{remaining_seconds:02d}"
            
            # 尝试获取视频质量信息
            if 'videoDetails' in result:
                video_details = result['videoDetails']
                quality_info = {}
                
                if 'lengthSeconds' in video_details:
                    seconds = int(video_details['lengthSeconds'])
                    minutes = seconds // 60
                    remaining_seconds = seconds % 60
                    metadata['length_time'] = f"{minutes}:{remaining_seconds:02d}"
                
                # 添加其他质量相关信息
                for key in ['shortDescription', 'keywords', 'channelId', 'isLiveContent']:
                    if key in video_details:
                        quality_info[key] = video_details[key]
                
                metadata['video_quality_info'] = quality_info
                
        except Exception as e:
            logger.warning(f"提取视频质量信息时出错: {e}")

    def save_to_json(self, metadata, output_file):
        """
        将元信息保存为JSON文件
        """
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)
            logger.info(f"元信息已保存到: {output_file}")
            return True
        except Exception as e:
            logger.error(f"保存JSON文件失败: {e}")
            return False


def main():
    parser = argparse.ArgumentParser(description='获取YouTube视频元信息')
    parser.add_argument('video_input', help='YouTube视频ID、短链接或完整URL')
    parser.add_argument('-o', '--output', help='输出JSON文件路径', default=None)
    parser.add_argument('--proxy', action='store_true', help='使用代理')
    parser.add_argument('--print-only', action='store_true', help='只打印到控制台，不保存文件')
    
    args = parser.parse_args()
    
    # 创建YouTube元信息获取器
    youtube_meta = YouTubeVideoMeta(use_proxy=args.proxy)
    
    # 获取视频元信息
    metadata = youtube_meta.get_video_metadata(args.video_input)
    
    if metadata is None:
        logger.error("无法获取视频元信息")
        sys.exit(1)
    
    # 打印元信息
    print(json.dumps(metadata, ensure_ascii=False, indent=2))
    
    # 保存到文件（如果需要）
    if not args.print_only:
        if args.output:
            output_file = args.output
        else:
            output_file = f"{metadata['video_id']}_metadata.json"
        
        youtube_meta.save_to_json(metadata, output_file)


if __name__ == '__main__':
    main()
